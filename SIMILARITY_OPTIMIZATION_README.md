# Similarity Optimization Documentation

## Overview

The `find_nearest_image` function in `app/services/similarity.py` has been optimized to improve performance by using sklear<PERSON>'s `cosine_similarity` function for batch computation instead of calculating cosine similarity one by one in a loop.

## Changes Made

### 1. Added sklearn import
```python
from sklearn.metrics.pairwise import cosine_similarity
```

### 2. Renamed original method
The original `find_nearest_image` method has been renamed to `find_nearest_image_old` to preserve the original implementation for reference and potential rollback.

### 3. Implemented optimized method
A new `find_nearest_image` method has been created that:
- Maintains the same interface and return structure
- Uses batch computation with sklearn's `cosine_similarity`
- Processes all feature vectors at once instead of one by one

## Performance Benefits

### Before (Original Implementation)
```python
def find_nearest_image_old(self, img, min_score = 0.7):
    result = []
    nearests = self.repo_vector.find_nearest_image(img)
    for nr in nearests:
        score = cosine(np.squeeze(img), nr['feature'])  # Individual calculation
        if score < min_score: continue
        result.append({'url': nr['url'], 'similarity': float("{:.2f}".format(score)),'detail': self.repo.fetch_image_detail(nr['url'])})
    return result
```

**Issues:**
- O(n) individual cosine similarity calculations
- Each calculation involves separate numpy operations
- No vectorization benefits

### After (Optimized Implementation)
```python
def find_nearest_image(self, img, min_score = 0.7):
    result = []
    nearests = self.repo_vector.find_nearest_image(img)
    
    if not nearests:
        return result
    
    # Extract features and URLs from nearests
    features = []
    urls = []
    for nr in nearests:
        features.append(nr['feature'])
        urls.append(nr['url'])
    
    # Convert to numpy array for batch processing
    features_matrix = np.array(features)
    img_squeezed = np.squeeze(img).reshape(1, -1)
    
    # Calculate all similarities at once using sklearn
    similarities = cosine_similarity(img_squeezed, features_matrix)[0]
    
    # Filter results based on min_score and build result list
    for i, score in enumerate(similarities):
        if score >= min_score:
            result.append({
                'url': urls[i], 
                'similarity': float("{:.2f}".format(score)),
                'detail': self.repo.fetch_image_detail(urls[i])
            })
    
    return result
```

**Benefits:**
- Single batch computation using optimized sklearn implementation
- Leverages BLAS/LAPACK optimizations
- Better memory access patterns
- Reduced function call overhead

## Expected Performance Improvements

- **CPU Usage**: Reduced due to vectorized operations
- **Memory Efficiency**: Better cache utilization with batch processing
- **Execution Time**: Significant reduction, especially with larger datasets
- **Scalability**: Better performance scaling with number of nearest images

## Compatibility

- ✅ **Interface**: Identical function signature
- ✅ **Return Format**: Same data structure returned
- ✅ **Behavior**: Equivalent results (within floating-point precision)
- ✅ **Dependencies**: sklearn already included in Pipfile

## Testing

The optimization maintains mathematical equivalence with the original implementation:

1. **Cosine Similarity Equivalence**: sklearn's `cosine_similarity` produces identical results to the original `cosine` function
2. **Method Equivalence**: Both old and new methods return the same results for identical inputs
3. **Edge Cases**: Handles empty results and edge cases correctly

## Rollback Plan

If any issues are discovered, the optimization can be easily rolled back by:

1. Renaming `find_nearest_image` to `find_nearest_image_new`
2. Renaming `find_nearest_image_old` back to `find_nearest_image`
3. Removing the sklearn import if not used elsewhere

## Usage

The optimized function is used automatically in:
- `get_similar_images()` method
- All API endpoints that perform similarity search
- Batch similarity operations

No changes are required in calling code as the interface remains identical.

## Monitoring

To monitor the performance improvement:
1. Check the execution time logs in `get_similar_images()` method
2. Monitor CPU usage during similarity operations
3. Track memory usage patterns
4. Measure API response times for similarity endpoints

## Dependencies

- **sklearn**: Already included in project dependencies
- **numpy**: Already included (used by sklearn)
- **Backward Compatibility**: Original implementation preserved as `find_nearest_image_old`

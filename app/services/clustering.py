from domain.repositories.similarity_repository import SimilarityRepository
from domain.repositories.similarity_vector import SimilarityVector
from sklearn.preprocessing import normalize
from sklearn.cluster import AgglomerativeClustering
from sklearn.cluster import DBSCAN
import numpy as np
import logging
import time 

class Clustering:
    def __init__(self, repo: SimilarityRepository, vectorRepo: SimilarityVector):
        logging.info('init repo clustering...')
        self.repo = repo
        self.vectorRepo = vectorRepo

        similarity_threshold = 0.5
        self.distance_threshold = 1 - similarity_threshold # This will be our 'eps'
        # self.build_cluster()

    def build_cluster(self):
        start = time.time() 
        logging.info('build image cluster')
        data = self.vectorRepo.fetch_all()
        #create cluster
        cluster_ids = self._get_cluster_ids(data)
        logging.info("cluster built, update db...")
        updates = []
        for url, cluster in cluster_ids:
            updates.append((url, {"cluster_id": int(cluster)}))

        logging.info('clusterIds build, now update database...')
        self.repo.update_batch(updates)
        logging.info(f"finish clustering, took {(time.time() - start)/60} minutes")

    def _get_cluster_ids(self, data):        
        logging.info(f"total data from vector db {len(data)}")
        urls = [r['url'] for r in data]
        features = [r['feature'] for r in data]
        logging.info(f'type of feature {type(features[0])}')

        vectors = np.array([np.array(vec) for vec in features])
        vectors_normalized = normalize(vectors)        

        # Perform clustering
        model = AgglomerativeClustering(
            n_clusters=None,           # Let distance_threshold decide clusters
            distance_threshold=self.distance_threshold,    # Adjust based on your data
            linkage="average",          # Other options: "ward", "complete", "single"
            metric='cosine'
        )

        logging.info(f"build clustering for {len(vectors_normalized)} vectors...")
        cluster_ids = model.fit_predict(vectors_normalized)

        #analyze results
        unique_cluster_ids, counts = np.unique(cluster_ids, return_counts=True)
        clusters_with_more_than_one = unique_cluster_ids[counts > 1]
        print(f"Cluster IDs with more than one element ({len(clusters_with_more_than_one)} items):")
        print(clusters_with_more_than_one)

        return zip(urls, cluster_ids)

    def _get_cluster_ids_dbscan(self, data):
        logging.info("_get_cluster_ids_dbscan...")
        urls = [r['url'] for r in data]
        features = [r['feature'] for r in data]
        logging.info(f'type of feature {type(features[0])}')

        vectors = np.array([np.array(vec) for vec in features])
        vectors_normalized = normalize(vectors)

        dbscan = DBSCAN(
            eps=self.distance_threshold, 
            min_samples=2, 
            metric='cosine'
        )

        logging.info("fit data for clustering...")
        dbscan.fit(vectors_normalized)
        labels = dbscan.labels_
        logging.info(f"labels: {labels[:15]}")
        num_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"\nFound {num_clusters} clusters.")
        logging.info("finish")        

        cluster_ids = []
        ungroup_count = 1
        max = -1     
        for i, label in enumerate(labels):
            if label == -1:                
                cluster_ids.append(ungroup_count+num_clusters) #just use random cluster id
                ungroup_count += 1
            else:
                cluster_ids.append(label)
                if label > max: max = label

        logging.info(f"max: {max}")
        logging.info(f"cluster_ids: {cluster_ids[:15]}")
        return zip(urls, cluster_ids)




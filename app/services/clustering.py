from domain.repositories.similarity_repository import SimilarityRepository
from domain.repositories.similarity_vector import SimilarityVector
from sklearn.preprocessing import normalize
from sklearn.cluster import AgglomerativeClustering
from sklearn.cluster import DBSCAN
import numpy as np
import logging
import time
from typing import Optional
from sklearn.metrics.pairwise import cosine_similarity

class Clustering:
    def __init__(self, repo: SimilarityRepository, vectorRepo: SimilarityVector, similarity_service=None):
        logging.info('init repo clustering...')
        self.repo = repo
        self.vectorRepo = vectorRepo
        self.similarity_service = similarity_service

        similarity_threshold = 0.5
        self.distance_threshold = 1 - similarity_threshold # This will be our 'eps'
        # self.build_cluster()

    def build_cluster(self):
        start = time.time() 
        logging.info('build image cluster')
        data = self.vectorRepo.fetch_all()
        #create cluster
        cluster_ids = self._get_cluster_ids(data)
        logging.info("cluster built, update db...")
        updates = []
        for url, cluster in cluster_ids:
            updates.append((url, {"cluster_id": int(cluster)}))

        logging.info('clusterIds build, now update database...')
        self.repo.update_batch(updates)
        logging.info(f"finish clustering, took {(time.time() - start)/60} minutes")

    def _get_cluster_ids(self, data):        
        logging.info(f"total data from vector db {len(data)}")
        urls = [r['url'] for r in data]
        features = [r['feature'] for r in data]
        logging.info(f'type of feature {type(features[0])}')

        vectors = np.array([np.array(vec) for vec in features])
        vectors_normalized = normalize(vectors)        

        # Perform clustering
        model = AgglomerativeClustering(
            n_clusters=None,           # Let distance_threshold decide clusters
            distance_threshold=self.distance_threshold,    # Adjust based on your data
            linkage="average",          # Other options: "ward", "complete", "single"
            metric='cosine'
        )

        logging.info(f"build clustering for {len(vectors_normalized)} vectors...")
        cluster_ids = model.fit_predict(vectors_normalized)

        #analyze results
        unique_cluster_ids, counts = np.unique(cluster_ids, return_counts=True)
        clusters_with_more_than_one = unique_cluster_ids[counts > 1]
        print(f"Cluster IDs with more than one element ({len(clusters_with_more_than_one)} items):")
        print(clusters_with_more_than_one)

        return zip(urls, cluster_ids)

    def _get_cluster_ids_dbscan(self, data):
        logging.info("_get_cluster_ids_dbscan...")
        urls = [r['url'] for r in data]
        features = [r['feature'] for r in data]
        logging.info(f'type of feature {type(features[0])}')

        vectors = np.array([np.array(vec) for vec in features])
        vectors_normalized = normalize(vectors)

        dbscan = DBSCAN(
            eps=self.distance_threshold, 
            min_samples=2, 
            metric='cosine'
        )

        logging.info("fit data for clustering...")
        dbscan.fit(vectors_normalized)
        labels = dbscan.labels_
        logging.info(f"labels: {labels[:15]}")
        num_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"\nFound {num_clusters} clusters.")
        logging.info("finish")        

        cluster_ids = []
        ungroup_count = 1
        max = -1     
        for i, label in enumerate(labels):
            if label == -1:                
                cluster_ids.append(ungroup_count+num_clusters) #just use random cluster id
                ungroup_count += 1
            else:
                cluster_ids.append(label)
                if label > max: max = label

        logging.info(f"max: {max}")
        logging.info(f"cluster_ids: {cluster_ids[:15]}")
        return zip(urls, cluster_ids)

    def assign_cluster_ids(self, similarity_threshold: float = 0.7):
        """
        Assign cluster IDs to data that doesn't have cluster_id.

        Process:
        1. Find one data without cluster_id
        2. Extract its features and find similar images using vectorRepo
        3. From similar images that match threshold, find cluster_id from repo
        4. Assign the same cluster_id, or create new one if no similar found
        5. Loop until all data has cluster_id

        Args:
            similarity_threshold: Minimum similarity score to consider images as similar (default: 0.7)
        """
        if not self.similarity_service:
            logging.error("SimilarityService is required for cluster assignment")
            return

        logging.info(f"Starting cluster assignment with similarity threshold: {similarity_threshold}")
        processed_count = 0

        while True:
            # Step 1: Find one data without cluster_id
            unassigned_data = self.repo.find_one_without_cluster_id()

            if not unassigned_data:
                logging.info(f"All data has been assigned cluster IDs. Total processed: {processed_count}")
                break

            url = unassigned_data['url']
            logging.info(f"Processing image without cluster_id: {url}")

            try:
                # Step 2: Extract features for this image
                img_feature = self.similarity_service.extract_image_feature(image_url=url)

                if img_feature.shape[0] == 0:
                    logging.warning(f"Failed to extract features for {url}, skipping...")
                    # Assign a new cluster ID to avoid infinite loop
                    max_cluster_id = self.repo.get_max_cluster_id()
                    new_cluster_id = max_cluster_id + 1
                    self.repo.update_cluster_id(url, new_cluster_id)
                    processed_count += 1
                    continue

                # Step 3: Find similar images using vectorRepo
                similar_images = self.vectorRepo.find_nearest_image(img_feature, top_n=20)

                assigned_cluster_id = None

                # Step 4: Check similarity scores and find cluster_id from matching images
                for similar_img in similar_images:
                    similar_url = similar_img['url']
                    similar_feature = similar_img['feature']

                    # Calculate similarity score
                    img_squeezed = np.squeeze(img_feature).reshape(1, -1)
                    similar_squeezed = np.squeeze(similar_feature).reshape(1, -1)
                    similarity_score = cosine_similarity(img_squeezed, similar_squeezed)[0][0]

                    logging.debug(f"Similarity with {similar_url}: {similarity_score:.3f}")

                    # If similarity meets threshold, get cluster_id from repo
                    if similarity_score >= similarity_threshold:
                        similar_data = self.repo.fetch_image_detail(similar_url)
                        if similar_data:
                            # Handle both dict and Photo object
                            if isinstance(similar_data, dict):
                                cluster_id = similar_data.get('cluster_id')
                            else:
                                cluster_id = getattr(similar_data, 'cluster_id', None)

                            if cluster_id is not None:
                                assigned_cluster_id = cluster_id
                                logging.info(f"Found similar image {similar_url} with cluster_id {assigned_cluster_id}, similarity: {similarity_score:.3f}")
                                break

                # Step 5: Assign cluster_id
                if assigned_cluster_id is not None:
                    # Assign the same cluster_id as the similar image
                    self.repo.update_cluster_id(url, assigned_cluster_id)
                    logging.info(f"Assigned existing cluster_id {assigned_cluster_id} to {url}")
                else:
                    # No similar image found, create new cluster_id
                    max_cluster_id = self.repo.get_max_cluster_id()
                    new_cluster_id = max_cluster_id + 1
                    self.repo.update_cluster_id(url, new_cluster_id)
                    logging.info(f"No similar images found, assigned new cluster_id {new_cluster_id} to {url}")

                processed_count += 1

                # Log progress every 10 images
                if processed_count % 10 == 0:
                    logging.info(f"Progress: {processed_count} images processed")

            except Exception as e:
                logging.error(f"Error processing image {url}: {e}")
                # Assign a new cluster ID to avoid infinite loop
                max_cluster_id = self.repo.get_max_cluster_id()
                new_cluster_id = max_cluster_id + 1
                self.repo.update_cluster_id(url, new_cluster_id)
                processed_count += 1
                continue

        logging.info(f"Cluster assignment completed. Total images processed: {processed_count}")




from domain.repositories.similarity_vector import SimilarityVector
from pymilvus import MilvusClient, DataType
from os import environ
import logging
import time
import numpy as np
from utils.cast import ensure_float32

class MilvusVector(SimilarityVector):
    def __init__(self):
        logging.info('init milvus...')
        self.client = MilvusClient(
            uri=environ.get('MILVUS_URI'),
            token=environ.get('MILVUS_TOKEN')
        )
        
        self.collection_name = environ.get('VECTOR_COLLECTION_NAME', 'photos')
        logging.info(f"Connecting to DB: {environ.get('MILVUS_URI')}, collection name {self.collection_name}")

        # Check if collection exists, if not create it with matching schema
        if not self.client.has_collection(self.collection_name):
            self._create_collection()
        else:
            logging.info(f'collection exists: {self.client.has_collection(self.collection_name)}')

    def _create_collection(self):
        """Create a new collection matching the schema of the original photos collection"""
        logging.info(f"Creating new collection: {self.collection_name}")
        
        # Create schema
        vector_dim = int(environ.get("FEATURE_VECTOR_DIM", 1280))
        schema = self.client.create_schema()
        schema.add_field("id", DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field("feature", DataType.FLOAT_VECTOR, dim=vector_dim)
        schema.add_field("url", DataType.VARCHAR, max_length=512)
        
        # Prepare index parameters
        index_params = self.client.prepare_index_params()
        index_params.add_index("feature", metric_type="COSINE")
        
        # Create collection with schema and index parameters
        self.client.create_collection(
            collection_name=self.collection_name,
            schema=schema,
            index_params=index_params
        )
        
        logging.info(f"Successfully created collection: {self.collection_name}")

    def find_nearest_image(self, img_feature, top_n=20):
        start = time.time() 
        search_params = {"metric_type": "COSINE", "params": {"level": 2}}
        # logging.info(f"find_nearest_image, type {type(img_feature)}")
        # if isinstance(img_feature, np.ndarray):
        #     logging.info(f"vector type {img_feature.dtype}")
        
        img_feature = ensure_float32(img_feature)
        # logging.info(f"vector type {type(img_feature)}, dtype: {img_feature.dtype}, shape: {np.squeeze(img_feature).shape}")
        results = self.client.search(
            collection_name=self.collection_name,
            data=[np.squeeze(img_feature)],
            limit=top_n,
            search_params=search_params,
            anns_field="feature",
            output_fields=["url", "feature"]
        )
                
        if time.time() - start > 5.0:
                logging.info(f'[performance] find_nearest_image milvus took { time.time() - start}')

        if not results:
            logging.info('---- Milvus return no data')
            return []

        result = []
        for item in results[0]:
            if isinstance(item['feature'], list):
                item['feature'] = np.array(item['feature'])
                
            result.append({
                'url': item['url'],
                'feature': item['feature']
            })

        # logging.info(f"result.....{len(result)}")
        return result

    def save(self, vector_data):
        logging.info(f'save to milvus vector db.... shape: {vector_data[0]['vector'].shape}')
        data_key = {v['url']:v['vector'] for v in vector_data}

        # check if exist
        urls = [v['url'] for v in vector_data]
        logging.info(f'total urls: {len(urls)}')
        exist_urls = self.fetch_by_url(urls)
        logging.info(f'exist urls: {len(exist_urls)}')
        for url in exist_urls:
            del data_key[url]

        logging.info(f'not exist data: {len(data_key)}')
        if len(data_key) == 0:
            logging.info('skip add to vector db....')
            return 

        # build data 
        rows = []
        for url in data_key: 
            rows.append({
                'url': url,
                'feature': data_key[url]
            })

        logging.info(f'total rows: {len(rows)}')
        self.client.insert(
            collection_name=self.collection_name,
            data=rows
        )
        self.client.flush(self.collection_name)
        logging.info('--- save finish....')

    def fetch_by_url(self, urls):
        link_in = "','".join(urls)
        res = self.client.query(
            collection_name=self.collection_name,
            filter=f"url in ['{link_in}']",
            output_fields=["url"]
        )
        
        return [row['url'] for row in res]

    def fetch_all(self):
        iterator = self.client.query_iterator(batch_size=100, collection_name=self.collection_name, output_fields=["url", "feature"])
        results = []

        while True:
            result = iterator.next()
            if not result:
                iterator.close()
                break
            results.extend([{'url':row['url'], 'feature': row['feature']} for row in result])

        print(f' --- all data : {len(results)}')
        print(results[0]['url'])
        return results
        # res = self.client.query(
        #     collection_name=self.collection_name,
        #     output_fields=["url", "feature"],
        #     limit=1
        #     )
        # print(f'type: {type(res)}, data: {res}')
        # return res